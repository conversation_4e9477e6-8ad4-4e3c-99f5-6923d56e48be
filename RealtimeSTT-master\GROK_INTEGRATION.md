# Grok API Integration for RealtimeSTT

This document describes the Grok API integration that has been added to the RealtimeSTT project, replacing all previous AI service integrations.

## Overview

The RealtimeSTT project now exclusively uses xAI's Grok API for text generation and AI processing capabilities. All previous integrations with OpenAI, Azure Speech, ElevenLabs, and other AI services have been removed.

## Features

### Grok Client (`RealtimeSTT.GrokClient`)

The new `GrokClient` class provides:

- **Authentication**: Secure API key-based authentication with xAI
- **Text Generation**: Both streaming and non-streaming text generation
- **Chat Completions**: Full conversation support with message history
- **Error Handling**: Comprehensive error handling for API failures, rate limits, and network issues
- **Retry Logic**: Automatic retry with exponential backoff for transient failures
- **OpenAI Compatibility**: API structure similar to OpenAI for easy migration

### Key Methods

```python
from RealtimeSTT import GrokClient

# Initialize client
client = GrokClient(api_key="your-xai-api-key")

# Simple text generation
response = client.generate_text("Hello, how are you?")

# Streaming text generation
for chunk in client.generate_stream("Tell me a story"):
    print(chunk, end="")

# Chat completion
messages = [{"role": "user", "content": "Hello!"}]
response = client.chat_completion(messages=messages)

# List available models
models = client.list_models()
```

## Setup Instructions

### 1. Get xAI API Key

1. Visit [xAI Console](https://console.x.ai/)
2. Create an account or sign in
3. Navigate to API Keys section
4. Generate a new API key

### 2. Set API Key

**Method 1: Using .env File (Recommended)**

1. Edit the `.env` file in the project root:
   ```
   XAI_API_KEY=your-actual-api-key-here
   ```

2. Replace the placeholder with your actual API key

**Method 2: Environment Variable (Alternative)**

**Windows:**
```cmd
set XAI_API_KEY=your-api-key-here
```

**Linux/macOS:**
```bash
export XAI_API_KEY=your-api-key-here
```

> **Note:** All example files have been updated to automatically load the API key from the `.env` file. See `SETUP_GROK_API.md` for detailed setup instructions.

### 3. Install Dependencies

The Grok integration requires the `requests` library, which is now included in the requirements:

```bash
pip install -r requirements.txt
```

## Updated Examples

All example files have been updated to use Grok API:

### Basic Talkbot (`tests/minimalistic_talkbot.py`)
```python
import RealtimeSTT, RealtimeTTS
import os

grok_client = RealtimeSTT.GrokClient(api_key=os.environ.get("XAI_API_KEY"))
stream = RealtimeTTS.TextToAudioStream(RealtimeTTS.SystemEngine())
recorder = RealtimeSTT.AudioToTextRecorder(model="medium")

def generate(messages):
    for chunk in grok_client.chat_completion(model="grok-4", messages=messages, stream=True):
        if "choices" in chunk and len(chunk["choices"]) > 0:
            delta = chunk["choices"][0].get("delta", {})
            if "content" in delta and delta["content"]:
                yield delta["content"]
```

### Voice Interface (`tests/openai_voice_interface.py`)
- Updated to use Grok API instead of OpenAI
- Maintains same user interface and functionality
- Uses SystemEngine for text-to-speech (removed Azure/ElevenLabs)

### Advanced Talk (`tests/advanced_talk.py`)
- Grok API integration for conversation
- Voice selection using SystemEngine only
- Removed Azure and ElevenLabs dependencies

### Translator (`tests/translator.py`)
- Real-time translation using Grok API
- SystemEngine for text-to-speech output
- Support for multiple languages

## Configuration Changes

### Environment Variables
- **Removed**: `OPENAI_API_KEY`, `AZURE_SPEECH_KEY`, `ELEVENLABS_API_KEY`
- **Added**: `XAI_API_KEY`

### Batch Files (`example_app/start.bat`)
```batch
:: xAI Grok API Key  https://console.x.ai/team/default/api-keys
set XAI_API_KEY=
```

### Dependencies
- **Removed**: `openai` package dependencies
- **Added**: `requests>=2.25.0` for HTTP API calls

## Available Models

Current Grok models include:
- `grok-4` (recommended, latest model)
- `grok-2` (previous generation)

Use `client.list_models()` to get the current list of available models.

## Error Handling

The Grok client includes comprehensive error handling:

```python
from RealtimeSTT import GrokClient, GrokAPIError

try:
    client = GrokClient(api_key="your-key")
    response = client.generate_text("Hello")
except GrokAPIError as e:
    if e.status_code == 401:
        print("Invalid API key")
    elif e.status_code == 429:
        print("Rate limit exceeded")
    else:
        print(f"API error: {e}")
```

## Testing

### Unit Tests
Run the comprehensive test suite:
```bash
python tests/test_grok_integration.py
```

### Example Script
Test the integration with a simple example:
```bash
python tests/grok_example.py --test-grok-only
```

### Live Integration Test
Test with speech-to-text (requires microphone):
```bash
python tests/grok_example.py
```

## Migration Notes

### From OpenAI
- Replace `openai.ChatCompletion.create()` with `grok_client.chat_completion()`
- Update model names from `gpt-*` to `grok-*`
- Change API key environment variable from `OPENAI_API_KEY` to `XAI_API_KEY`

### From Azure Speech
- All Azure Speech functionality has been replaced with SystemEngine
- Remove Azure Speech key and region configurations
- Update TTS engine initialization to use `SystemEngine()`

### From ElevenLabs
- ElevenLabs TTS has been replaced with SystemEngine
- Remove ElevenLabs API key configuration
- Update voice selection to use system voices

## API Compatibility

The Grok API is designed to be compatible with OpenAI's API structure, making migration straightforward:

```python
# OpenAI style (still works)
response = client.chat_completion(
    model="grok-4",
    messages=[{"role": "user", "content": "Hello"}],
    stream=True
)

# Convenience methods
text = client.generate_text("Hello")
for chunk in client.generate_stream("Hello"):
    print(chunk)
```

## Troubleshooting

### Common Issues

1. **Import Error**: Make sure you're importing from the correct path
2. **API Key Error**: Verify `XAI_API_KEY` is set correctly
3. **Rate Limits**: The client automatically handles rate limiting with retries
4. **Network Issues**: Check internet connection and firewall settings

### Debug Mode

Enable debug logging for troubleshooting:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Support

For issues related to:
- **Grok API**: Contact xAI support or check [xAI Documentation](https://docs.x.ai/)
- **RealtimeSTT Integration**: Create an issue in the RealtimeSTT repository
- **Speech Recognition**: Refer to RealtimeSTT documentation for Whisper-related issues

## Changelog

### Removed
- OpenAI API integration and dependencies
- Azure Speech API integration
- ElevenLabs API integration
- Related configuration files and environment variables

### Added
- Complete Grok API client implementation
- Streaming and non-streaming text generation
- Comprehensive error handling and retry logic
- Updated example files and documentation
- Test suite for Grok integration

### Modified
- All example files to use Grok API
- Configuration files to use XAI_API_KEY
- Dependencies to include requests library
- Documentation to reflect Grok-only usage
