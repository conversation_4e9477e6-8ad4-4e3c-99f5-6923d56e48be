# RealtimeSTT

Real-time speech-to-text transcription library with voice activity detection.

> **Project Status: Community-Driven**
> This project is community-maintained. Pull requests are welcome.

## Features

- Real-time voice transcription
- Voice activity detection
- Wake word activation
- Client-server architecture support
- GPU acceleration support

## Requirements

- Python 3.6+
- Microphone access
- Optional: CUDA-compatible GPU for better performance

## Quick Start

### Basic Usage

```python
from RealtimeSTT import AudioToTextRecorder

def process_text(text):
    print(text)

if __name__ == '__main__':
    recorder = AudioToTextRecorder()
    while True:
        recorder.text(process_text)
```

### Auto-typing

```python
from RealtimeSTT import AudioToTextRecorder
import pyautogui

def process_text(text):
    pyautogui.typewrite(text + " ")

if __name__ == '__main__':
    recorder = AudioToTextRecorder()
    while True:
        recorder.text(process_text)
```

## Installation

```bash
pip install RealtimeSTT
```

### Platform-specific Prerequisites

**Linux:**
```bash
sudo apt-get update
sudo apt-get install python3-dev portaudio19-dev
```

**macOS:**
```bash
brew install portaudio
```

### GPU Support (Recommended)

For better performance, install CUDA support:

**CUDA 11.8:**
```bash
pip install torch==2.5.1+cu118 torchaudio==2.5.1 --index-url https://download.pytorch.org/whl/cu118
```

**CUDA 12.X:**
```bash
pip install torch==2.5.1+cu121 torchaudio==2.5.1 --index-url https://download.pytorch.org/whl/cu121
```

### CUDA Setup (First-time users)

1. **Install NVIDIA CUDA Toolkit** from [NVIDIA's website](https://developer.nvidia.com/cuda-toolkit-archive)
2. **Install cuDNN** from [NVIDIA's cuDNN page](https://developer.nvidia.com/cudnn-downloads)
3. **Install ffmpeg** (optional):
   - Ubuntu/Debian: `sudo apt install ffmpeg`
   - macOS: `brew install ffmpeg`
   - Windows: `winget install Gyan.FFmpeg`

## Usage Examples

### Manual Recording

```python
from RealtimeSTT import AudioToTextRecorder

if __name__ == '__main__':
    recorder = AudioToTextRecorder()
    recorder.start()
    input("Press Enter to stop recording...")
    recorder.stop()
    print("Transcription:", recorder.text())
```

### Automatic Recording (Voice Activity Detection)

```python
from RealtimeSTT import AudioToTextRecorder

if __name__ == '__main__':
    with AudioToTextRecorder() as recorder:
        print("Transcription:", recorder.text())
```

### Continuous Transcription

```python
from RealtimeSTT import AudioToTextRecorder

def process_text(text):
    print(text)

if __name__ == '__main__':
    recorder = AudioToTextRecorder()
    while True:
        recorder.text(process_text)
```

### Wake Words

```python
from RealtimeSTT import AudioToTextRecorder

if __name__ == '__main__':
    recorder = AudioToTextRecorder(wake_words="jarvis")
    print('Say "Jarvis" to start recording.')
    print(recorder.text())
```

Available wake words: alexa, americano, blueberry, bumblebee, computer, grapefruits, grasshopper, hey google, hey siri, jarvis, ok google, picovoice, porcupine, terminator.

### Callbacks

```python
from RealtimeSTT import AudioToTextRecorder

def start_callback():
    print("Recording started!")

def stop_callback():
    print("Recording stopped!")

if __name__ == '__main__':
    recorder = AudioToTextRecorder(
        on_recording_start=start_callback,
        on_recording_stop=stop_callback
    )
```

### Audio Input from File

```python
from RealtimeSTT import AudioToTextRecorder

if __name__ == '__main__':
    recorder = AudioToTextRecorder(use_microphone=False)
    with open("audio_chunk.pcm", "rb") as f:
        audio_chunk = f.read()

    recorder.feed_audio(audio_chunk)
    print("Transcription:", recorder.text())
```

## Examples

See the `tests/` directory for example scripts:

- `simple_test.py` - Basic usage demonstration
- `realtimestt_test.py` - Live transcription demo
- `auto_typing_example.py` - Auto-typing functionality
- `grok_example.py` - Integration with Grok API (requires XAI_API_KEY)
- `translator.py` - Real-time translation
- `openai_voice_interface.py` - Voice interface for AI APIs

The `example_app/` directory contains a complete PyQt5 application.

## Configuration

### Key Parameters

```python
recorder = AudioToTextRecorder(
    model="base",                    # Model: tiny, base, small, medium, large-v1, large-v2
    language="en",                   # Language code (auto-detect if empty)
    device="cuda",                   # "cuda" or "cpu"
    input_device_index=0,            # Microphone device index
    use_microphone=True,             # False to use feed_audio() instead

    # Voice Activity Detection
    silero_sensitivity=0.6,          # 0-1, higher = more sensitive
    webrtc_sensitivity=3,            # 0-3, higher = less sensitive
    post_speech_silence_duration=0.2, # Silence duration to end recording

    # Wake Words
    wake_words="jarvis",             # Comma-separated wake words
    wake_words_sensitivity=0.6,      # 0-1, wake word sensitivity

    # Callbacks
    on_recording_start=callback_func,
    on_recording_stop=callback_func,
    on_transcription_start=callback_func,

    # Performance
    batch_size=16,                   # Transcription batch size
    beam_size=5,                     # Beam search size
    compute_type="default",          # Quantization type
)

### Real-time Transcription

```python
recorder = AudioToTextRecorder(
    enable_realtime_transcription=True,
    realtime_model_type="tiny",
    on_realtime_transcription_update=callback_func
)
```

### Advanced Configuration

For complete parameter documentation, see the source code. Key categories:
- **Model settings**: model size, language, compute type
- **Audio settings**: device indices, microphone usage
- **VAD settings**: sensitivity, silence duration, buffer settings
- **Wake word settings**: backend, sensitivity, timeout
- **Performance settings**: batch size, beam size, threading
- **Callback functions**: recording, transcription, VAD, wake word events

## OpenWakeWord

### Custom Models

Train custom wake word models using [OpenWakeWord](https://github.com/dscripka/openWakeWord). Convert TensorFlow models to ONNX:

```bash
pip install -U tf2onnx
python -m tf2onnx.convert --tflite model.tflite --output model.onnx
```

### Usage

```python
recorder = AudioToTextRecorder(
    wakeword_backend="oww",
    wake_words_sensitivity=0.35,
    openwakeword_model_paths="word1.onnx,word2.onnx",
    wake_word_buffer_duration=1
)
```

## Troubleshooting

**cuDNN Error**: If you encounter cuDNN library errors, either:
- Downgrade: `pip install ctranslate2==4.4.0`
- Upgrade cuDNN to version 9.2+

**Multiprocessing**: Use `if __name__ == '__main__':` protection in your scripts.

## License

MIT License - see [LICENSE](LICENSE) file.

## Author

Kolja Beigel ([GitHub](https://github.com/KoljaB/RealtimeSTT))
