# Auto-Typing Feature

RealtimeSTT now includes built-in auto-typing functionality that automatically types transcribed speech into any active text input field. This feature simulates keyboard input, allowing dictated text to appear in any application with a focused text cursor.

## Overview

The auto-typing feature transforms RealtimeSTT from a speech-to-text library into a system-wide dictation tool. Instead of just returning transcribed text, it can automatically type the text as if you were physically typing on the keyboard.

### Key Features

- **System-wide compatibility**: Works with any application that accepts keyboard input
- **Real-time typing**: Supports both final transcription and real-time stabilized text typing
- **Configurable typing speed**: Adjustable delay between keystrokes
- **Safety features**: Built-in fail-safe mechanisms
- **Callback support**: Hooks for typing events (start, complete, error)
- **Automatic spacing**: Optional space insertion after each transcription

## Quick Start

### Basic Auto-Typing

```python
from RealtimeSTT import AudioToTextRecorder

# Enable auto-typing with default settings
recorder = AudioToTextRecorder(enable_auto_typing=True)

print("Speak and watch your words appear in any text field!")
while True:
    recorder.text()  # Text will be automatically typed
```

### Advanced Configuration

```python
from RealtimeSTT import AudioToTextRecorder

def on_typing_start():
    print("Started typing...")

def on_typing_complete():
    print("Finished typing!")

def on_typing_error(error):
    print(f"Typing error: {error}")

recorder = AudioToTextRecorder(
    # Enable auto-typing
    enable_auto_typing=True,
    
    # Typing configuration
    auto_typing_delay=0.01,        # 10ms between keystrokes
    auto_typing_fail_safe=True,    # Enable emergency stop
    auto_typing_add_space=True,    # Add space after each transcription
    
    # Callbacks
    on_auto_typing_start=on_typing_start,
    on_auto_typing_complete=on_typing_complete,
    on_auto_typing_error=on_typing_error,
)
```

## Configuration Parameters

### Auto-Typing Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `enable_auto_typing` | bool | False | Enable/disable auto-typing functionality |
| `auto_typing_delay` | float | 0.01 | Delay between keystrokes in seconds |
| `auto_typing_fail_safe` | bool | True | Enable fail-safe (move mouse to corner to stop) |
| `auto_typing_add_space` | bool | True | Add space after each transcription |
| `on_auto_typing_start` | callable | None | Callback when typing starts |
| `on_auto_typing_complete` | callable | None | Callback when typing completes |
| `on_auto_typing_error` | callable | None | Callback when typing error occurs |

### Typing Speed Guidelines

- **0.001-0.005**: Very fast typing (may cause issues with some applications)
- **0.01**: Fast typing (recommended for most use cases)
- **0.02-0.05**: Normal typing speed
- **0.1+**: Slow typing (useful for applications with input lag)

## Real-Time Auto-Typing

For real-time typing as you speak, enable real-time transcription:

```python
recorder = AudioToTextRecorder(
    # Enable real-time transcription
    enable_realtime_transcription=True,
    realtime_model_type="tiny",  # Fast model for real-time
    
    # Enable auto-typing for stabilized text
    enable_auto_typing=True,
    auto_typing_delay=0.005,  # Fast typing for real-time feel
)

# Real-time typing happens automatically with stabilized text
```

## Safety Features

### Fail-Safe Mechanism

When `auto_typing_fail_safe=True` (default), you can stop auto-typing by moving your mouse to the top-left corner of the screen. This is a pyautogui safety feature.

### Error Handling

The auto-typing system includes comprehensive error handling:

```python
def handle_typing_error(error):
    print(f"Auto-typing failed: {error}")
    # Handle the error (e.g., retry, log, notify user)

recorder = AudioToTextRecorder(
    enable_auto_typing=True,
    on_auto_typing_error=handle_typing_error
)
```

## Use Cases

### 1. Document Dictation
```python
# For writing documents, emails, etc.
recorder = AudioToTextRecorder(
    enable_auto_typing=True,
    auto_typing_delay=0.02,  # Comfortable typing speed
    model="base",  # Good accuracy for documents
)
```

### 2. Real-Time Chat/Messaging
```python
# For instant messaging, chat applications
recorder = AudioToTextRecorder(
    enable_realtime_transcription=True,
    enable_auto_typing=True,
    auto_typing_delay=0.005,  # Fast typing
    realtime_model_type="tiny",  # Quick response
)
```

### 3. Form Filling
```python
# For filling out forms, web applications
recorder = AudioToTextRecorder(
    enable_auto_typing=True,
    auto_typing_delay=0.01,
    auto_typing_add_space=False,  # No extra spaces in forms
)
```

## Requirements

Auto-typing requires the `pyautogui` library:

```bash
pip install pyautogui
```

The library is automatically added to requirements when you install RealtimeSTT with auto-typing support.

## Platform Compatibility

- **Windows**: Full support
- **macOS**: Full support (may require accessibility permissions)
- **Linux**: Full support with X11 or Wayland

### macOS Permissions

On macOS, you may need to grant accessibility permissions to your Python application or terminal for auto-typing to work.

## Examples

See the following example files:

- `tests/simple_auto_typing.py` - Basic auto-typing example
- `tests/auto_typing_example.py` - Comprehensive auto-typing demo
- `tests/realtime_auto_typing_example.py` - Real-time auto-typing
- `tests/type_into_textbox.py` - Updated with built-in functionality

## Troubleshooting

### Common Issues

1. **"pyautogui not found"**: Install with `pip install pyautogui`
2. **Typing not working**: Ensure the target application has focus
3. **Typing too fast/slow**: Adjust `auto_typing_delay` parameter
4. **Permission errors on macOS**: Grant accessibility permissions

### Debug Mode

Enable debug logging to troubleshoot issues:

```python
import logging
logging.basicConfig(level=logging.DEBUG)

recorder = AudioToTextRecorder(
    enable_auto_typing=True,
    level=logging.DEBUG
)
```

## Migration from Manual pyautogui

If you were previously using manual pyautogui integration:

### Before (Manual)
```python
import pyautogui
from RealtimeSTT import AudioToTextRecorder

def type_text(text):
    pyautogui.typewrite(text + " ")

recorder = AudioToTextRecorder()
while True:
    recorder.text(type_text)
```

### After (Built-in)
```python
from RealtimeSTT import AudioToTextRecorder

recorder = AudioToTextRecorder(enable_auto_typing=True)
while True:
    recorder.text()  # Automatically typed
```

The built-in solution provides better error handling, configuration options, and integration with real-time transcription.
