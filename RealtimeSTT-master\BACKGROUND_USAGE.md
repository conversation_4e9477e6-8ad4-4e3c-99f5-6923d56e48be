# RealtimeSTT Wake Word Auto-Typing - Background Usage Guide

This guide explains how to run the RealtimeSTT wake word auto-typing application in the background on Windows, including setup for automatic startup and troubleshooting.

## Quick Start

### Available Applications

**Standard Version (Real-time enabled):**
```bash
python wake_word_auto_typing.py
```

### Run in Background (Command Prompt)
```cmd
# Standard version
start /B python wake_word_auto_typing.py
```

### Run in Background (PowerShell)
```powershell
# Standard version
Start-Process python -ArgumentList "wake_word_auto_typing.py" -WindowStyle Hidden

### Required Dependencies
Ensure all dependencies are installed:
```bash
pip install RealtimeSTT pyautogui pvporcupine
```

### Hardware Requirements
- Microphone (built-in or external)
- Windows 10/11
- Python 3.8 or higher


### Initial Setup Verification
1. Test the application first in foreground mode:
   ```bash
   python wake_word_auto_typing.py
   # or
   python wake_word_realtime_typing.py
   ```
2. Verify wake word detection works by saying "computer"
3. Test auto-typing in a text editor (Notepad, Word, etc.)
4. Notice how text appears as you speak (real-time transcription)

## Background Operation Methods

### Method 1: Windows Task Scheduler (Recommended for Auto-Start)

#### Create a Batch File
1. Create `start_wake_word.bat` in the RealtimeSTT directory:
```batch
@echo off
cd /d "C:\path\to\RealtimeSTT-master"
python wake_word_auto_typing.py
```

#### Setup Task Scheduler
1. Open Task Scheduler (`taskschd.msc`)
2. Click "Create Basic Task"
3. Name: "RealtimeSTT Wake Word"
4. Trigger: "When I log on"
5. Action: "Start a program"
6. Program: `C:\path\to\RealtimeSTT-master\start_wake_word.bat`
7. Check "Run with highest privileges"

### Method 2: Windows Service (Advanced)

#### Using NSSM (Non-Sucking Service Manager)
1. Download NSSM from https://nssm.cc/download
2. Extract and run as administrator:
```cmd
nssm install RealtimeSTT
```
3. Configure:
   - Path: `C:\Python\python.exe` (your Python path)
   - Startup directory: `C:\path\to\RealtimeSTT-master`
   - Arguments: `wake_word_auto_typing.py`
4. Start service:
```cmd
nssm start RealtimeSTT
```

### Method 3: Startup Folder
1. Press `Win + R`, type `shell:startup`
2. Copy `start_wake_word.bat` to the startup folder
3. Application will start automatically on login

## Running Commands

### Start in Background
```cmd
# Command Prompt (visible window, runs in background)
start python wake_word_auto_typing.py

# PowerShell (hidden window)
Start-Process python -ArgumentList "wake_word_auto_typing.py" -WindowStyle Hidden

# Using pythonw (no console window)
pythonw wake_word_auto_typing.py
```

### Check if Running
```cmd
# Find Python processes
tasklist | findstr python

# More detailed process info
wmic process where "name='python.exe'" get processid,commandline
```

### Stop the Application
```cmd
# Find and kill by process name
taskkill /f /im python.exe

# Kill specific process (replace PID with actual process ID)
taskkill /f /pid 1234
```

## Usage Instructions

### Basic Operation
1. Start the application using any method above
2. Open any text editor, word processor, or text input field
3. Click in the text field to give it focus
4. Say "jarvis" to activate speech recognition
5. Speak your message clearly
6. The text will be automatically typed into the active field

### Emergency Stop
- Move mouse cursor to the top-left corner of the screen (pyautogui fail-safe)
- Or use Ctrl+C if running in a visible terminal
- Or kill the process using Task Manager or command line

## Configuration Options

### Modify Wake Word Sensitivity
Edit `wake_word_auto_typing.py` line ~128:
```python
wake_words_sensitivity=0.6,  # Adjust between 0.0-1.0
```

### Change Wake Word
Edit `wake_word_auto_typing.py` line ~127:
```python
wake_words="computer",  # Change to supported wake words
```

Supported wake words: computer, alexa, hey google, hey siri, jarvis, porcupine, terminator

### Adjust Typing Speed
Edit `wake_word_auto_typing.py` line ~140:
```python
auto_typing_delay=0.01,  # Increase for slower typing
```

## Troubleshooting

### Common Issues

#### "No module named 'RealtimeSTT'"
```bash
pip install RealtimeSTT
```

#### "No module named 'pyautogui'"
```bash
pip install pyautogui
```

#### "No module named 'pvporcupine'"
```bash
pip install pvporcupine
```

#### Wake Word Not Detected
- Check microphone permissions in Windows Settings
- Increase wake word sensitivity in configuration
- Test microphone with other applications
- Ensure microphone is set as default recording device

#### Auto-typing Not Working
- Ensure target application has focus (click in text field)
- Check if pyautogui fail-safe is triggered (mouse in top-left corner)
- Verify pyautogui installation: `python -c "import pyautogui; print('OK')"`

#### High CPU Usage
- This is normal during active listening and transcription
- CPU usage should decrease when not actively processing speech

#### Application Stops Unexpectedly
- Check Windows Event Viewer for error details
- Run in foreground mode first to see error messages
- Ensure all dependencies are properly installed

### Logging and Debugging

#### Enable Verbose Logging
Modify the script to add logging:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

#### Check Process Status
```cmd
# List all Python processes
tasklist /fi "imagename eq python.exe"

# Check specific process details
wmic process where "commandline like '%wake_word_auto_typing%'" get processid,commandline
```

## Performance Tips

1. **Close unnecessary applications** to reduce CPU load
2. **Use a good quality microphone** for better wake word detection
3. **Position microphone properly** - avoid background noise
4. **Run as administrator** if experiencing permission issues
5. **Keep Windows audio drivers updated**

## Security Considerations

- The application has access to your microphone and keyboard input
- All processing is done locally - no data sent to external servers
- Consider running with limited user privileges if possible
- Be aware that the application can type into any active text field

## Support

For issues and support:
1. Check the main RealtimeSTT documentation
2. Verify all prerequisites are met
3. Test in foreground mode first
4. Check Windows Event Viewer for detailed error messages

---

**Note**: This application uses local speech recognition and does not require an internet connection once properly installed.
