"""
Utility module to load environment variables from .env file.
This ensures consistent .env loading across all scripts.
"""

import os
from pathlib import Path


def load_env():
    """
    Load environment variables from .env file.
    
    This function looks for a .env file in the current directory or parent directories
    and loads the environment variables from it.
    """
    # Find the .env file (look in current dir and parent dirs)
    current_dir = Path(__file__).parent
    env_file = None
    
    # Check current directory and up to 3 parent directories
    for i in range(4):
        potential_env = current_dir / '.env'
        if potential_env.exists():
            env_file = potential_env
            break
        current_dir = current_dir.parent
    
    if env_file and env_file.exists():
        print(f"Loading environment variables from: {env_file}")
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                # Skip empty lines and comments
                if line and not line.startswith('#'):
                    if '=' in line:
                        key, value = line.split('=', 1)
                        key = key.strip()
                        value = value.strip()
                        # Remove quotes if present
                        if value.startswith('"') and value.endswith('"'):
                            value = value[1:-1]
                        elif value.startswith("'") and value.endswith("'"):
                            value = value[1:-1]
                        
                        # Only set if not already in environment
                        if key not in os.environ:
                            os.environ[key] = value
        return True
    else:
        print("No .env file found. Please create one with your XAI_API_KEY.")
        return False


def get_api_key():
    """
    Get the XAI API key from environment variables.
    Loads .env file if needed.
    
    Returns:
        str: The API key, or None if not found
    """
    # Try to load .env file first
    load_env()
    
    api_key = os.environ.get("XAI_API_KEY")
    if not api_key or api_key == "your-xai-api-key-here":
        print("❌ XAI_API_KEY not found or not set properly!")
        print("Please edit the .env file and set your actual API key.")
        return None
    
    return api_key


if __name__ == "__main__":
    # Test the .env loading
    print("Testing .env file loading...")
    if load_env():
        api_key = os.environ.get("XAI_API_KEY")
        if api_key and api_key != "your-xai-api-key-here":
            print(f"✅ API key loaded successfully: {api_key[:10]}...")
        else:
            print("⚠️  API key placeholder found. Please set your actual API key in .env file.")
    else:
        print("❌ Failed to load .env file.")
