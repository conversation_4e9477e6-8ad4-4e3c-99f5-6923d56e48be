"""
Auto-typing module for RealtimeSTT

This module provides functionality to automatically type transcribed text
into any active text input field using system-wide keyboard simulation.
"""

import logging
import time
import threading
from typing import Optional, Callable, Union

try:
    import pyautogui
    PYAUTOGUI_AVAILABLE = True
except ImportError:
    PYAUTOGUI_AVAILABLE = False
    pyautogui = None

logger = logging.getLogger(__name__)


class AutoTyper:
    """
    Handles automatic typing of transcribed text into active applications.
    
    This class provides system-wide text input capability that can type into
    any application with an active text cursor, simulating keyboard input.
    """
    
    def __init__(self,
                 typing_delay: float = 0.01,
                 fail_safe: bool = True,
                 add_space_after: bool = True,
                 on_typing_start: Optional[Callable] = None,
                 on_typing_complete: Optional[Callable] = None,
                 on_typing_error: Optional[Callable[[Exception], None]] = None):
        """
        Initialize the AutoTyper.
        
        Args:
            typing_delay (float): Delay between keystrokes in seconds. Default: 0.01
            fail_safe (bool): Enable pyautogui fail-safe (move mouse to corner to stop). Default: True
            add_space_after (bool): Add a space after each typed text segment. Default: True
            on_typing_start (callable, optional): Callback when typing starts
            on_typing_complete (callable, optional): Callback when typing completes
            on_typing_error (callable, optional): Callback when typing error occurs
        """
        if not PYAUTOGUI_AVAILABLE:
            raise ImportError(
                "pyautogui is required for auto-typing functionality. "
                "Install it with: pip install pyautogui"
            )
        
        self.typing_delay = typing_delay
        self.add_space_after = add_space_after
        self.on_typing_start = on_typing_start
        self.on_typing_complete = on_typing_complete
        self.on_typing_error = on_typing_error
        
        # Configure pyautogui
        pyautogui.FAILSAFE = fail_safe
        pyautogui.PAUSE = typing_delay
        
        # Thread safety
        self._typing_lock = threading.Lock()
        self._is_typing = False
        
        logger.info(f"AutoTyper initialized with delay: {typing_delay}s, "
                   f"fail_safe: {fail_safe}, add_space_after: {add_space_after}")
    
    def type_text(self, text: str, async_typing: bool = False) -> None:
        """
        Type the given text into the currently active application.
        
        Args:
            text (str): Text to type
            async_typing (bool): If True, typing will be performed in a separate thread
        """
        if not text or not text.strip():
            return
        
        if async_typing:
            threading.Thread(target=self._type_text_sync, args=(text,), daemon=True).start()
        else:
            self._type_text_sync(text)
    
    def _type_text_sync(self, text: str) -> None:
        """
        Synchronously type text with error handling and callbacks.
        
        Args:
            text (str): Text to type
        """
        with self._typing_lock:
            if self._is_typing:
                logger.warning("Already typing, skipping new text input")
                return
            
            self._is_typing = True
        
        try:
            # Callback for typing start
            if self.on_typing_start:
                try:
                    self.on_typing_start()
                except Exception as e:
                    logger.error(f"Error in on_typing_start callback: {e}")
            
            # Clean and prepare text
            clean_text = self._prepare_text(text)
            
            if clean_text:
                logger.debug(f"Typing text: '{clean_text}'")
                
                # Type the text
                pyautogui.typewrite(clean_text, interval=self.typing_delay)
                
                # Add space if configured
                if self.add_space_after:
                    pyautogui.typewrite(" ", interval=self.typing_delay)
                
                logger.debug("Text typing completed successfully")
            
            # Callback for typing complete
            if self.on_typing_complete:
                try:
                    self.on_typing_complete()
                except Exception as e:
                    logger.error(f"Error in on_typing_complete callback: {e}")
                    
        except Exception as e:
            logger.error(f"Error during text typing: {e}")
            
            # Callback for typing error
            if self.on_typing_error:
                try:
                    self.on_typing_error(e)
                except Exception as callback_error:
                    logger.error(f"Error in on_typing_error callback: {callback_error}")
        
        finally:
            with self._typing_lock:
                self._is_typing = False
    
    def _prepare_text(self, text: str) -> str:
        """
        Prepare text for typing by cleaning and formatting.
        
        Args:
            text (str): Raw text to prepare
            
        Returns:
            str: Cleaned text ready for typing
        """
        if not text:
            return ""
        
        # Strip whitespace and ensure single spaces
        clean_text = " ".join(text.strip().split())
        
        # Remove or replace problematic characters that might cause issues
        # with pyautogui typing
        replacements = {
            '\n': ' ',
            '\r': ' ',
            '\t': ' ',
        }
        
        for old, new in replacements.items():
            clean_text = clean_text.replace(old, new)
        
        return clean_text
    
    def is_typing(self) -> bool:
        """
        Check if currently typing.
        
        Returns:
            bool: True if currently typing, False otherwise
        """
        with self._typing_lock:
            return self._is_typing
    
    def set_typing_delay(self, delay: float) -> None:
        """
        Update the typing delay.
        
        Args:
            delay (float): New delay between keystrokes in seconds
        """
        self.typing_delay = delay
        pyautogui.PAUSE = delay
        logger.debug(f"Typing delay updated to: {delay}s")
    
    def set_fail_safe(self, enabled: bool) -> None:
        """
        Enable or disable pyautogui fail-safe.
        
        Args:
            enabled (bool): Whether to enable fail-safe
        """
        pyautogui.FAILSAFE = enabled
        logger.debug(f"Fail-safe {'enabled' if enabled else 'disabled'}")


def create_auto_typer(**kwargs) -> AutoTyper:
    """
    Factory function to create an AutoTyper instance.
    
    Args:
        **kwargs: Arguments to pass to AutoTyper constructor
        
    Returns:
        AutoTyper: Configured AutoTyper instance
    """
    return AutoTyper(**kwargs)


# Convenience function for simple auto-typing
def auto_type_text(text: str, 
                  typing_delay: float = 0.01,
                  add_space_after: bool = True) -> None:
    """
    Convenience function to quickly type text without creating an AutoTyper instance.
    
    Args:
        text (str): Text to type
        typing_delay (float): Delay between keystrokes in seconds
        add_space_after (bool): Add a space after the text
    """
    if not PYAUTOGUI_AVAILABLE:
        raise ImportError(
            "pyautogui is required for auto-typing functionality. "
            "Install it with: pip install pyautogui"
        )
    
    typer = AutoTyper(typing_delay=typing_delay, add_space_after=add_space_after)
    typer.type_text(text)
