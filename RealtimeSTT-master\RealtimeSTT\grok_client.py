"""
Grok API Client for xAI's Grok language models.

This module provides a client for interacting with xAI's Grok API,
offering text generation capabilities with proper error handling,
authentication, and response parsing.

Author: RealtimeSTT Integration
"""

import os
import json
import time
import logging
from typing import Dict, List, Optional, Union, Iterator, Any
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# Set up logging
logger = logging.getLogger("realtimestt.grok")

class GrokAPIError(Exception):
    """Custom exception for Grok API errors."""
    def __init__(self, message: str, status_code: Optional[int] = None, error_type: Optional[str] = None):
        self.message = message
        self.status_code = status_code
        self.error_type = error_type
        super().__init__(self.message)

class GrokClient:
    """
    Client for interacting with xAI's Grok API.
    
    This client provides methods for text generation using Grok models,
    with support for both streaming and non-streaming responses.
    """
    
    def __init__(self, 
                 api_key: Optional[str] = None,
                 base_url: str = "https://api.x.ai/v1",
                 timeout: float = 60.0,
                 max_retries: int = 3):
        """
        Initialize the Grok API client.
        
        Args:
            api_key: xAI API key. If None, will try to get from XAI_API_KEY environment variable.
            base_url: Base URL for the xAI API.
            timeout: Request timeout in seconds.
            max_retries: Maximum number of retry attempts for failed requests.
        """
        self.api_key = api_key or os.environ.get("XAI_API_KEY")
        if not self.api_key:
            raise ValueError("API key is required. Set XAI_API_KEY environment variable or pass api_key parameter.")
        
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.max_retries = max_retries
        
        # Set up session with retry strategy
        self.session = requests.Session()
        try:
            # Try new parameter name first (urllib3 >= 1.26.0)
            retry_strategy = Retry(
                total=max_retries,
                status_forcelist=[429, 500, 502, 503, 504],
                allowed_methods=["HEAD", "GET", "POST"],
                backoff_factor=1
            )
        except TypeError:
            # Fall back to old parameter name for older urllib3 versions
            retry_strategy = Retry(
                total=max_retries,
                status_forcelist=[429, 500, 502, 503, 504],
                method_whitelist=["HEAD", "GET", "POST"],
                backoff_factor=1
            )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        # Set default headers
        self.session.headers.update({
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "User-Agent": "RealtimeSTT-GrokClient/1.0"
        })
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """
        Make a request to the Grok API with error handling.
        
        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint (without base URL)
            **kwargs: Additional arguments to pass to requests
            
        Returns:
            Response object
            
        Raises:
            GrokAPIError: If the request fails
        """
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        
        try:
            response = self.session.request(method, url, timeout=self.timeout, **kwargs)
            
            if response.status_code == 401:
                raise GrokAPIError("Invalid API key", status_code=401, error_type="authentication")
            elif response.status_code == 429:
                raise GrokAPIError("Rate limit exceeded", status_code=429, error_type="rate_limit")
            elif response.status_code >= 400:
                try:
                    error_data = response.json()
                    error_message = error_data.get("error", {}).get("message", f"HTTP {response.status_code}")
                except:
                    error_message = f"HTTP {response.status_code}: {response.text}"
                raise GrokAPIError(error_message, status_code=response.status_code)
            
            return response
            
        except requests.exceptions.Timeout:
            raise GrokAPIError("Request timeout", error_type="timeout")
        except requests.exceptions.ConnectionError:
            raise GrokAPIError("Connection error", error_type="connection")
        except requests.exceptions.RequestException as e:
            raise GrokAPIError(f"Request failed: {str(e)}", error_type="request")
    
    def list_models(self) -> List[Dict[str, Any]]:
        """
        List available Grok models.
        
        Returns:
            List of model information dictionaries
        """
        response = self._make_request("GET", "/models")
        data = response.json()
        return data.get("data", [])
    
    def chat_completion(self,
                       messages: List[Dict[str, str]],
                       model: str = "grok-4",
                       temperature: float = 0.7,
                       max_tokens: Optional[int] = None,
                       stream: bool = False,
                       **kwargs) -> Union[Dict[str, Any], Iterator[Dict[str, Any]]]:
        """
        Create a chat completion using Grok.
        
        Args:
            messages: List of message dictionaries with 'role' and 'content' keys
            model: Model to use for completion
            temperature: Sampling temperature (0.0 to 2.0)
            max_tokens: Maximum tokens to generate
            stream: Whether to stream the response
            **kwargs: Additional parameters to pass to the API
            
        Returns:
            Response dictionary or iterator of response chunks if streaming
        """
        payload = {
            "model": model,
            "messages": messages,
            "temperature": temperature,
            "stream": stream,
            **kwargs
        }
        
        if max_tokens is not None:
            payload["max_tokens"] = max_tokens
        
        if stream:
            return self._stream_completion(payload)
        else:
            response = self._make_request("POST", "/chat/completions", json=payload)
            return response.json()
    
    def _stream_completion(self, payload: Dict[str, Any]) -> Iterator[Dict[str, Any]]:
        """
        Handle streaming chat completion.
        
        Args:
            payload: Request payload
            
        Yields:
            Response chunks
        """
        response = self._make_request("POST", "/chat/completions", json=payload, stream=True)
        
        for line in response.iter_lines():
            if line:
                line = line.decode('utf-8')
                if line.startswith('data: '):
                    data = line[6:]  # Remove 'data: ' prefix
                    if data.strip() == '[DONE]':
                        break
                    try:
                        chunk = json.loads(data)
                        yield chunk
                    except json.JSONDecodeError:
                        continue
    
    def generate_text(self,
                     prompt: str,
                     model: str = "grok-4",
                     temperature: float = 0.7,
                     max_tokens: Optional[int] = None,
                     system_message: Optional[str] = None) -> str:
        """
        Generate text from a prompt (convenience method).
        
        Args:
            prompt: Input prompt
            model: Model to use
            temperature: Sampling temperature
            max_tokens: Maximum tokens to generate
            system_message: Optional system message
            
        Returns:
            Generated text
        """
        messages = []
        if system_message:
            messages.append({"role": "system", "content": system_message})
        messages.append({"role": "user", "content": prompt})
        
        response = self.chat_completion(
            messages=messages,
            model=model,
            temperature=temperature,
            max_tokens=max_tokens,
            stream=False
        )
        
        return response["choices"][0]["message"]["content"]
    
    def generate_stream(self,
                       prompt: str,
                       model: str = "grok-4",
                       temperature: float = 0.7,
                       max_tokens: Optional[int] = None,
                       system_message: Optional[str] = None) -> Iterator[str]:
        """
        Generate text from a prompt with streaming (convenience method).
        
        Args:
            prompt: Input prompt
            model: Model to use
            temperature: Sampling temperature
            max_tokens: Maximum tokens to generate
            system_message: Optional system message
            
        Yields:
            Text chunks
        """
        messages = []
        if system_message:
            messages.append({"role": "system", "content": system_message})
        messages.append({"role": "user", "content": prompt})
        
        for chunk in self.chat_completion(
            messages=messages,
            model=model,
            temperature=temperature,
            max_tokens=max_tokens,
            stream=True
        ):
            if "choices" in chunk and len(chunk["choices"]) > 0:
                delta = chunk["choices"][0].get("delta", {})
                if "content" in delta and delta["content"]:
                    yield delta["content"]

# Convenience function for backward compatibility
def create_grok_client(api_key: Optional[str] = None) -> GrokClient:
    """
    Create a Grok client instance.
    
    Args:
        api_key: xAI API key
        
    Returns:
        GrokClient instance
    """
    return GrokClient(api_key=api_key)
