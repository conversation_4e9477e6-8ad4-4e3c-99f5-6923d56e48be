#!/usr/bin/env python3
"""
Test script to verify duplicate text output is fixed.
This script will help you test the RealtimeSTT application without actually typing.
"""

import time
import threading
from wake_word_auto_typing import WakeWordAutoTyper

class TestAutoTyper(WakeWordAutoTyper):
    """Test version that prints instead of typing"""
    
    def __init__(self):
        super().__init__(enable_audio_feedback=False)
        self.typed_texts = []
        self.call_count = 0
    
    def process_text(self, text):
        """Override to capture text instead of typing"""
        import time
        import threading
        
        # Use lock to prevent concurrent execution
        with self.processing_lock:
            self.call_count += 1
            
            # Debug information
            current_time = time.strftime("%H:%M:%S")
            thread_id = threading.current_thread().ident
            print(f"[TEST {current_time}] process_text() called #{self.call_count}")
            print(f"[TEST] Thread ID: {thread_id}")
            print(f"[TEST] Input text: '{text}'")
            
            if not text or not text.strip():
                print(f"[TEST] Skipping empty text")
                return
            
            cleaned_text = text.strip()
            current_time_seconds = time.time()
            
            # Check for duplicate processing
            if (cleaned_text == self.last_processed_text and 
                current_time_seconds - self.last_process_time < 2.0):
                print(f"[TEST] DUPLICATE DETECTED! Would have been blocked")
                print(f"[TEST] Last processed: '{self.last_processed_text}' at {self.last_process_time}")
                return
            
            # Record the text instead of typing
            self.typed_texts.append(cleaned_text)
            print(f"[TEST] *** WOULD TYPE: '{cleaned_text}' ***")
            print(f"[TEST] Total texts captured: {len(self.typed_texts)}")
            
            # Update tracking variables
            self.last_processed_text = cleaned_text
            self.last_process_time = current_time_seconds
            
            print(f"[TEST] process_text() completed")

def test_duplicate_prevention():
    """Test the duplicate prevention mechanism"""
    print("Testing duplicate prevention mechanism...")
    
    typer = TestAutoTyper()
    
    # Test 1: Same text within 2 seconds should be blocked
    print("\n=== Test 1: Duplicate within 2 seconds ===")
    typer.process_text("Hello world")
    time.sleep(0.5)  # Wait 0.5 seconds
    typer.process_text("Hello world")  # Should be blocked
    
    # Test 2: Same text after 2 seconds should be allowed
    print("\n=== Test 2: Same text after 2 seconds ===")
    time.sleep(2.1)  # Wait more than 2 seconds
    typer.process_text("Hello world")  # Should be allowed
    
    # Test 3: Different text should always be allowed
    print("\n=== Test 3: Different text ===")
    typer.process_text("Different text")
    typer.process_text("Another text")
    
    print(f"\n=== Results ===")
    print(f"Total process_text() calls: {typer.call_count}")
    print(f"Texts that would be typed: {len(typer.typed_texts)}")
    print(f"Typed texts: {typer.typed_texts}")
    
    expected_texts = ["Hello world", "Hello world", "Different text", "Another text"]
    if typer.typed_texts == expected_texts:
        print("✅ Test PASSED: Duplicate prevention working correctly")
    else:
        print("❌ Test FAILED: Unexpected results")
        print(f"Expected: {expected_texts}")
        print(f"Got: {typer.typed_texts}")

if __name__ == "__main__":
    test_duplicate_prevention()
