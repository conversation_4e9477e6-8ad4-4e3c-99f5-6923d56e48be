"""
Latency Test Script for RealtimeSTT Optimizations

This script measures the latency between speech end and text output
to validate the optimizations made to the wake_word_auto_typing.py script.

Usage:
1. Run this script
2. Say "computer" (wake word)
3. Speak a test phrase
4. The script will measure and report the latency

The script will compare:
- Time when speech ends (detected by silence)
- Time when transcription is completed
- Total processing time
"""

import time
import threading
import os
import sys

# Handle Windows-specific torch audio initialization
if os.name == "nt" and (3, 8) <= sys.version_info < (3, 99):
    try:
        from torchaudio._extension.utils import _init_dll_path
        _init_dll_path()
    except ImportError:
        pass

from RealtimeSTT import AudioToTextRecorder


class LatencyTester:
    def __init__(self):
        self.speech_end_time = None
        self.transcription_start_time = None
        self.transcription_end_time = None
        self.recording_start_time = None
        self.wake_word_detected_time = None
        self.test_results = []
        
    def on_wakeword_detected(self):
        """Callback when wake word is detected"""
        self.wake_word_detected_time = time.time()
        print(f"[{time.strftime('%H:%M:%S')}] Wake word detected!")
        
    def on_recording_start(self):
        """Callback when recording starts"""
        self.recording_start_time = time.time()
        print(f"[{time.strftime('%H:%M:%S')}] Recording started - speak now!")
        
    def on_recording_stop(self):
        """Callback when recording stops"""
        self.speech_end_time = time.time()
        print(f"[{time.strftime('%H:%M:%S')}] Recording stopped - processing...")
        
    def on_vad_stop(self):
        """Callback when voice activity detection stops"""
        if not self.speech_end_time:  # Only set if not already set by recording stop
            self.speech_end_time = time.time()
            print(f"[{time.strftime('%H:%M:%S')}] Speech end detected by VAD")
    
    def measure_latency(self, text):
        """Measure and report latency metrics"""
        self.transcription_end_time = time.time()
        
        if self.speech_end_time and self.transcription_end_time:
            # Calculate latency from speech end to transcription completion
            latency = self.transcription_end_time - self.speech_end_time
            
            # Calculate total processing time from recording start
            total_time = self.transcription_end_time - self.recording_start_time if self.recording_start_time else 0
            
            # Calculate wake word to transcription time
            wake_to_transcription = self.transcription_end_time - self.wake_word_detected_time if self.wake_word_detected_time else 0
            
            print(f"\n{'='*60}")
            print(f"LATENCY MEASUREMENT RESULTS")
            print(f"{'='*60}")
            print(f"Transcribed text: '{text}'")
            print(f"Speech end to transcription: {latency:.3f} seconds")
            print(f"Total recording time: {total_time:.3f} seconds")
            print(f"Wake word to transcription: {wake_to_transcription:.3f} seconds")
            print(f"{'='*60}\n")
            
            # Store results for analysis
            self.test_results.append({
                'text': text,
                'latency': latency,
                'total_time': total_time,
                'wake_to_transcription': wake_to_transcription,
                'timestamp': time.strftime('%H:%M:%S')
            })
            
        # Reset timers for next test
        self.reset_timers()
        
    def reset_timers(self):
        """Reset all timing variables for next test"""
        self.speech_end_time = None
        self.transcription_start_time = None
        self.transcription_end_time = None
        self.recording_start_time = None
        self.wake_word_detected_time = None
        
    def print_summary(self):
        """Print summary of all test results"""
        if not self.test_results:
            print("No test results to summarize.")
            return
            
        print(f"\n{'='*60}")
        print(f"LATENCY TEST SUMMARY ({len(self.test_results)} tests)")
        print(f"{'='*60}")
        
        latencies = [r['latency'] for r in self.test_results]
        avg_latency = sum(latencies) / len(latencies)
        min_latency = min(latencies)
        max_latency = max(latencies)
        
        print(f"Average latency: {avg_latency:.3f} seconds")
        print(f"Minimum latency: {min_latency:.3f} seconds")
        print(f"Maximum latency: {max_latency:.3f} seconds")
        print(f"{'='*60}")
        
        for i, result in enumerate(self.test_results, 1):
            print(f"Test {i} [{result['timestamp']}]: {result['latency']:.3f}s - '{result['text']}'")
        print()
    
    def run_test(self):
        """Run the latency test"""
        print("Latency Test for RealtimeSTT Optimizations")
        print("==========================================")
        print("This test measures the delay between speech end and text output.")
        print("Say 'computer' then speak a test phrase.")
        print("Press Ctrl+C to exit and see summary.\n")
        
        try:
            # Initialize recorder with optimized settings
            recorder = AudioToTextRecorder(
                # Model configuration
                model="small",
                language="en",
                
                # Real-time transcription - disabled for cleaner testing
                enable_realtime_transcription=False,
                
                # Wake word configuration
                wakeword_backend="pvporcupine",
                wake_words="computer",
                wake_words_sensitivity=0.6,
                wake_word_timeout=3,
                wake_word_activation_delay=0,
                
                # Optimized voice activity detection settings
                silero_sensitivity=0.3,
                webrtc_sensitivity=2,
                post_speech_silence_duration=0.4,
                min_length_of_recording=0.3,
                
                # Optimized audio quality settings
                beam_size=3,
                beam_size_realtime=1,
                faster_whisper_vad_filter=True,
                normalize_audio=True,
                
                # Early transcription for reduced latency
                early_transcription_on_silence=2,
                
                # Callbacks for timing measurement
                on_wakeword_detected=self.on_wakeword_detected,
                on_recording_start=self.on_recording_start,
                on_recording_stop=self.on_recording_stop,
                on_vad_stop=self.on_vad_stop,
                
                # Disable auto-typing for testing
                enable_auto_typing=False,
                
                # UI settings
                spinner=False,
                no_log_file=True,
            )
            
            test_count = 0
            while True:
                try:
                    print(f"Test #{test_count + 1} - Say 'computer' then speak...")
                    text = recorder.text()
                    
                    if text and text.strip():
                        test_count += 1
                        self.measure_latency(text.strip())
                    else:
                        print("No text detected, try again.")
                        self.reset_timers()
                        
                except KeyboardInterrupt:
                    print("\nStopping latency test...")
                    break
                except Exception as e:
                    print(f"Error during test: {e}")
                    self.reset_timers()
                    
        except Exception as e:
            print(f"Failed to initialize recorder: {e}")
            return
            
        finally:
            try:
                recorder.shutdown()
            except:
                pass
            self.print_summary()


def main():
    tester = LatencyTester()
    tester.run_test()


if __name__ == "__main__":
    main()
